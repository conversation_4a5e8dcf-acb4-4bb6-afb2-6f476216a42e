# 🌤️ Smart Weather Mobile App

AI-powered comprehensive weather application with intelligent life recommendations.

## 🚀 Features

### 🌡️ Weather Tab
- **Real-time weather data** for all 81 Turkish provinces
- **Current temperature** display with city information
- **AI-powered recommendations**:
  - 👔 **Clothing advice** based on temperature
  - 🎯 **Activity suggestions** for the day
  - 💊 **Health tips** for weather conditions

### ⭐ Favorites Tab
- **Save favorite cities** for quick access
- **One-tap weather check** - click any favorite city
- **Auto-navigation** to weather results
- **Easy management** - add/remove favorites

### 🌾 Agriculture Tab
- **Farming guidance** based on weather conditions
- **🌱 Planting advice** - what to plant when
- **💧 Irrigation recommendations** - optimal watering schedules
- **🚜 Field work suggestions** - best times for farming activities
- **Temperature-based insights** for agricultural planning

### ⚙️ Settings Tab
- **🔔 Notification controls** - enable/disable weather alerts
- **🌡️ Temperature units** - switch between Celsius/Fahrenheit
- **📱 App information** - version and developer details
- **🎨 Customization options**

## 🛠️ Tech Stack

- **Frontend**: React Native + Expo
- **Language**: TypeScript
- **Backend**: Mastra Framework
- **AI**: Gemini 2.0 Flash
- **Weather API**: Open-Meteo
- **Design**: Glass-morphism UI with modern UX

## 📱 Installation

```bash
# Clone the repository
git clone https://github.com/ErenYlskn/weather_forecast_mobile.git

# Navigate to project directory
cd weather_forecast_mobile

# Install dependencies
npm install

# Start the development server
npx expo start
```

## 🎯 Usage

1. **Search Weather**: Enter any Turkish city name
2. **Get Recommendations**: View AI-powered clothing, activity, and health advice
3. **Save Favorites**: Add frequently checked cities to favorites
4. **Agriculture Guidance**: Check farming recommendations in the Agriculture tab
5. **Customize Settings**: Adjust notifications and temperature units

## 🌍 Supported Locations

All 81 provinces of Turkey including:
- İstanbul, Ankara, İzmir, Antalya
- Samsun, Trabzon, Erzurum, Van
- Hakkari, Şırnak, Mardin, Diyarbakır
- And 73 more provinces...

## 🤖 AI Features

- **Smart Clothing Recommendations**: Based on temperature ranges
- **Activity Suggestions**: Weather-appropriate activities
- **Health Advice**: Seasonal health tips
- **Agriculture Guidance**: Farming recommendations by weather

## 📸 Screenshots

*Coming soon - app screenshots will be added*

## 🔧 Development

```bash
# Run on iOS simulator
npx expo start --ios

# Run on Android emulator
npx expo start --android

# Run on web
npx expo start --web
```

## 📄 License

MIT License - feel free to use and modify

## 👨‍💻 Developer

Created with ❤️ by AI Weather Assistant Team

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

---

**Smart Weather App - Your Life Assistant** 🌤️📱
