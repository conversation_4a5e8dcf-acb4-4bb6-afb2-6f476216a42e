{"compilerOptions": {"strict": false, "jsx": "react-jsx", "lib": ["es2018", "dom"], "target": "es2018", "module": "commonjs", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "noImplicitAny": false, "noEmit": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"], "extends": "expo/tsconfig.base"}